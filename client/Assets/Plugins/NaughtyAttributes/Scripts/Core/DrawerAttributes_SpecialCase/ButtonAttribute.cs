using System;

namespace NaughtyAttributes
{
    public enum EButtonEnableMode
    {
        /// <summary>
        /// But<PERSON> should be active always
        /// </summary>
        Always,
        /// <summary>
        /// But<PERSON> should be active only in editor
        /// </summary>
        Editor,
        /// <summary>
        /// But<PERSON> should be active only in playmode
        /// </summary>
        Playmode
    }

    [AttributeUsage(AttributeTargets.Method, AllowMultiple = false, Inherited = true)]
    public class ButtonAttribute : SpecialCaseDrawerAttribute
    {
        public string Text { get; private set; }
        public EButtonEnableMode SelectedEnableMode { get; private set; }

        public ButtonAttribute(string text = null, EButtonEnableMode enabledMode = EButtonEnableMode.Always)
        {
            Text = text;
            SelectedEnableMode = enabledMode;
        }
    }
}
