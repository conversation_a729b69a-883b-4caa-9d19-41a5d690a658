using System.Collections.Generic;
using System.Linq;
using Cysharp.Threading.Tasks;
using Global.Backend;
using Internal;
using Shared;

namespace Global.GameServices
{
    public interface IDeckService
    {
        IReadOnlyDictionary<int, IDeckConfiguration> Configurations { get; }
        IViewableProperty<int> SelectedIndex { get; }
        IViewableDelegate Initialized { get; }

        UniTask OnUpdated(IDeckConfiguration configuration);
        void SetIndex(int selectedIndex);
    }

    public class DeckService : IDeckService, IBackendProjection<BackendUserContexts.DeckProjection>
    {
        public DeckService(
            ICardsRegistry cardsRegistry,
            IBackendUser user,
            IBackendClient client,
            IReadOnlyLifetime lifetime)
        {
            _cardsRegistry = cardsRegistry;
            _user = user;
            _client = client;
            _lifetime = lifetime;
        }

        private readonly Dictionary<int, IDeckConfiguration> _configurations = new();
        private readonly ViewableProperty<int> _selectedIndex = new(0);
        private readonly ICardsRegistry _cardsRegistry;
        private readonly IBackendUser _user;
        private readonly IBackendClient _client;
        private readonly IReadOnlyLifetime _lifetime;
        private readonly ViewableDelegate _initialized = new();

        public IReadOnlyDictionary<int, IDeckConfiguration> Configurations => _configurations;
        public IViewableProperty<int> SelectedIndex => _selectedIndex;

        public IViewableDelegate Initialized => _initialized;

        public UniTask OnReceived(BackendUserContexts.DeckProjection data)
        {
            var isFirstPass = _configurations.Count == 0;
            
            foreach (var (index, entry) in data.Entries)
            {
                var configuration = GetOrCreateConfiguration(index);
                var cards = new List<ICardDefinition>();

                foreach (var cardType in entry.Cards)
                {
                    var definition = _cardsRegistry.Cards[cardType];
                    cards.Add(definition);
                }

                configuration.Update(cards);
            }

            _selectedIndex.Set(data.SelectedIndex);
            
            if (isFirstPass)
                _initialized.Invoke();

            return UniTask.CompletedTask;
        }

        public UniTask OnUpdated(IDeckConfiguration configuration)
        {
            var request = new BackendUserContexts.UpdateDeckRequest()
            {
                UserId = _user.Id,
                DeckIndex = configuration.Index,
                Cards = configuration.Cards.Select(card => card.Type).ToList()
            };

            var endpoint = _client.Options.Url + BackendUserContexts.UpdateDeckEndpoint;
            return _client.PostJson(_lifetime, endpoint, request);
        }

        public void SetIndex(int selectedIndex)
        {
            
        }

        private IDeckConfiguration GetOrCreateConfiguration(int index)
        {
            if (_configurations.TryGetValue(index, out var configuration))
                return configuration;

            configuration = new DeckConfiguration(index);

            _configurations[index] = configuration;

            return configuration;
        }
    }
}